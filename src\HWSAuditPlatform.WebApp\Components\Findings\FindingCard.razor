@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject IFindingCategoryApiService FindingCategoryApiService
@inject ILogger<FindingCard> Logger

<div class="finding-card @GetSeverityClass()">
    <div class="finding-header">
        <div class="finding-title-section">
            @if (Finding.FindingCategoryId.HasValue && !string.IsNullOrEmpty(Finding.FindingCategoryColorCode))
            {
                <div class="category-indicator" style="background-color: @Finding.FindingCategoryColorCode;"></div>
            }
            @if (!string.IsNullOrEmpty(Finding.FindingCategoryIconName))
            {
                <i class="@GetIconClass(Finding.FindingCategoryIconName) me-2"></i>
            }
            <h6 class="finding-title">
                @if (!string.IsNullOrEmpty(Finding.FindingCode))
                {
                    <span class="finding-code">@Finding.FindingCode</span>
                }
                Finding #@Finding.Id.Substring(0, 8)
            </h6>
        </div>
        <div class="finding-actions">
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" @onclick="() => ViewDetails()">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a></li>
                    @if (AllowCategoryAssignment)
                    {
                        <li><a class="dropdown-item" href="#" @onclick="() => ShowCategoryModal()">
                            <i class="fas fa-tags me-2"></i>Assign Category
                        </a></li>
                    }
                    <li><a class="dropdown-item" href="#" @onclick="() => ViewCorrectiveActions()">
                        <i class="fas fa-tasks me-2"></i>Corrective Actions
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="finding-content">
        <p class="finding-description">@Finding.FindingDescription</p>
        
        <div class="finding-metadata">
            <div class="metadata-row">
                <span class="metadata-label">Severity:</span>
                <div class="badge-container">
                    <span class="badge badge-sm @GetSeverityBadgeClass()" title="@Finding.FindingSeverityLevel">@Finding.FindingSeverityLevel</span>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(Finding.FindingCategoryName))
            {
                <div class="metadata-row">
                    <span class="metadata-label">Category:</span>
                    <div class="badge-container">
                        <span class="badge badge-sm bg-info" title="@Finding.FindingCategoryName">@Finding.FindingCategoryName</span>
                    </div>
                </div>
            }

            <div class="metadata-row">
                <span class="metadata-label">Status:</span>
                <div class="badge-container">
                    <span class="badge badge-sm @GetStatusBadgeClass()" title="@Finding.Status">@Finding.Status</span>
                </div>
            </div>
            
            @if (Finding.HasResponsibleUser)
            {
                <div class="metadata-row">
                    <span class="metadata-label">Responsible:</span>
                    <span class="responsible-user">
                        <i class="fas fa-user me-1"></i>
                        @Finding.ResponsibleUserFullName
                    </span>
                </div>
            }
            
            @if (Finding.HasRetrospectiveAnalyst)
            {
                <div class="metadata-row">
                    <span class="metadata-label">Analyst:</span>
                    <span class="analyst-user">
                        <i class="fas fa-chart-line me-1"></i>
                        @Finding.RetrospectiveAnalystUserFullName
                    </span>
                </div>
            }
            
            @if (Finding.DueDate.HasValue)
            {
                <div class="metadata-row">
                    <span class="metadata-label">Due Date:</span>
                    <span class="due-date @(Finding.IsOverdue ? "overdue" : "")">
                        @Finding.DueDate.Value.ToString("MMM dd, yyyy")
                        @if (Finding.IsOverdue)
                        {
                            <i class="fas fa-exclamation-triangle ms-1"></i>
                        }
                    </span>
                </div>
            }
        </div>

        @if (Finding.CorrectiveActionCount > 0)
        {
            <div class="corrective-actions-summary">
                <small class="text-muted">
                    <i class="fas fa-tasks me-1"></i>
                    @Finding.CorrectiveActionCount corrective actions (@Finding.OpenCorrectiveActionCount open)
                </small>
            </div>
        }
    </div>

    <div class="finding-footer">
        <small class="text-muted">
            Created @Finding.CreatedAt.ToString("MMM dd, yyyy") in @Finding.AreaName
        </small>
    </div>
</div>

@if (showCategoryModal)
{
    <FindingCategoryAssignmentModal 
        @bind-IsVisible="showCategoryModal"
        FindingId="Finding.Id"
        CurrentCategoryId="Finding.FindingCategoryId"
        AuditTemplateId="AuditTemplateId"
        OnCategoryAssigned="OnCategoryAssigned" />
}

<style>
    .finding-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background: white;
        margin-bottom: 1rem;
        transition: box-shadow 0.2s ease;
    }

    .finding-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .finding-card.severity-critical {
        border-left: 4px solid #dc3545;
    }

    .finding-card.severity-major {
        border-left: 4px solid #fd7e14;
    }

    .finding-card.severity-minor {
        border-left: 4px solid #ffc107;
    }

    .finding-card.severity-observation {
        border-left: 4px solid #6c757d;
    }

    .finding-header {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .finding-title-section {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .category-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        margin-right: 0.5rem;
    }

    .finding-title {
        margin: 0;
        color: #495057;
    }

    .finding-code {
        color: #6c757d;
        font-weight: normal;
        margin-right: 0.5rem;
    }

    .finding-content {
        padding: 0.5rem 1rem;
    }

    .finding-description {
        margin-bottom: 0.75rem;
        color: #495057;
    }

    .finding-metadata {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .metadata-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 0; /* Allow flex items to shrink */
    }

    .metadata-label {
        font-weight: 500;
        color: #6c757d;
        min-width: 80px;
        font-size: 0.875rem;
        flex-shrink: 0; /* Prevent label from shrinking */
    }

    .metadata-row .badge-container {
        flex: 1;
        min-width: 0; /* Allow badge container to shrink */
    }

    .responsible-user,
    .analyst-user {
        color: #495057;
        font-size: 0.875rem;
    }

    .due-date.overdue {
        color: #dc3545;
        font-weight: 500;
    }

    .corrective-actions-summary {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #f8f9fa;
    }

    .finding-footer {
        padding: 0.5rem 1rem 1rem 1rem;
        border-top: 1px solid #f8f9fa;
    }

    @@media (max-width: 576px) {
        .finding-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .metadata-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .metadata-label {
            min-width: auto;
        }
    }
</style>

@code {
    [Parameter] public FindingDto Finding { get; set; } = new();
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public bool AllowCategoryAssignment { get; set; } = true;
    [Parameter] public EventCallback<FindingDto> OnFindingChanged { get; set; }

    private bool showCategoryModal = false;

    private string GetSeverityClass()
    {
        return Finding.FindingSeverityLevel.ToString().ToLower() switch
        {
            "critical" => "severity-critical",
            "major" => "severity-major", 
            "minor" => "severity-minor",
            "observation" => "severity-observation",
            _ => ""
        };
    }

    private string GetSeverityBadgeClass()
    {
        return Finding.FindingSeverityLevel.ToString().ToLower() switch
        {
            "critical" => "bg-danger",
            "major" => "bg-warning",
            "minor" => "bg-info",
            "observation" => "bg-secondary",
            _ => "bg-light"
        };
    }

    private string GetStatusBadgeClass()
    {
        return Finding.IsOpen ? "bg-warning" : "bg-success";
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }

    private void ViewDetails()
    {
        // TODO: Implement view details functionality
        Logger.LogInformation("View details for finding {FindingId}", Finding.Id);
    }

    private void ShowCategoryModal()
    {
        showCategoryModal = true;
    }

    private void ViewCorrectiveActions()
    {
        // TODO: Implement view corrective actions functionality
        Logger.LogInformation("View corrective actions for finding {FindingId}", Finding.Id);
    }

    private async Task OnCategoryAssigned()
    {
        showCategoryModal = false;
        await OnFindingChanged.InvokeAsync(Finding);
    }
}
