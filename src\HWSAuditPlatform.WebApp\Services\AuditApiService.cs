using System.Net.Http.Json;
using System.Text.Json;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.WebApp.Models;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of audit API service
/// </summary>
public class AuditApiService : IAuditApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<AuditApiService> _logger;

    public AuditApiService(
        AuthenticatedHttpClientService httpClient,
        ILogger<AuditApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<IEnumerable<Audit>> GetAuditsAsync()
    {
        return await GetAuditsAsync(null, null, null);
    }

    public async Task<IEnumerable<Audit>> GetAuditsAsync(string? searchTerm = null, string? status = null, int? templateId = null)
    {
        try
        {
            _logger.LogInformation("Fetching audits from API with search term: {SearchTerm}, status: {Status}, templateId: {TemplateId}",
                searchTerm, status, templateId);

            // Build query parameters
            var queryParams = new List<string> { "pageSize=100" }; // Get more items for now

            if (!string.IsNullOrWhiteSpace(searchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");

            if (!string.IsNullOrWhiteSpace(status))
                queryParams.Add($"status={Uri.EscapeDataString(status)}");

            if (templateId.HasValue)
                queryParams.Add($"templateId={templateId.Value}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/v1/audits?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PaginatedResult<AuditSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    // Convert AuditSummaryDto to Audit entities
                    var audits = apiResponse.Data.Items.Select(dto => new Audit
                    {
                        Id = dto.Id,
                        OverallStatus = dto.OverallStatus,
                        ScheduledDate = dto.ScheduledDate,
                        DueDate = dto.DueDate,
                        OverallScore = dto.OverallScore,
                        AuditTemplate = new AuditTemplate { TemplateName = dto.AuditTemplateName ?? "" },
                        Factory = new Factory { FactoryName = dto.FactoryName ?? "" },
                        Area = new Area { AreaName = dto.AreaName ?? "" }
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} audits", audits.Count);
                    return audits;
                }
            }

            _logger.LogWarning("Failed to fetch audits. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<Audit>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching audits");
            return Enumerable.Empty<Audit>();
        }
    }

    public async Task<Audit?> GetAuditByIdAsync(string id)
    {
        try
        {
            _logger.LogInformation("Fetching audit with ID {Id}", id);
            var response = await _httpClient.GetAsync($"api/v1/audits/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    // Convert AuditDto to Audit entity
                    var dto = apiResponse.Data;
                    var audit = new Audit
                    {
                        Id = dto.Id,
                        AuditTemplateId = dto.AuditTemplateId,
                        AssignmentType = dto.AssignmentType,
                        AssignedToUserGroupId = dto.AssignedToUserGroupId,
                        AssignedToUserId = dto.AssignedToUserId,
                        ScheduledDate = dto.ScheduledDate,
                        DueDate = dto.DueDate,
                        StartedAt = dto.StartedAt,
                        CompletedAt = dto.CompletedAt,
                        OverallStatus = dto.OverallStatus,
                        FactoryId = dto.FactoryId,
                        AreaId = dto.AreaId,
                        SubAreaId = dto.SubAreaId,
                        OverallScore = dto.OverallScore,
                        ManagerComments = dto.ManagerComments,
                        ReviewedByUserId = dto.ReviewedByUserId,
                        ReviewedAt = dto.ReviewedAt,
                        RecurringAuditSettingId = dto.RecurringAuditSettingId,
                        AuditTemplate = new AuditTemplate { TemplateName = dto.AuditTemplateName ?? "" },
                        Factory = new Factory { FactoryName = dto.FactoryName ?? "" },
                        Area = new Area { AreaName = dto.AreaName ?? "" }
                    };

                    _logger.LogInformation("Successfully fetched audit with ID {Id}", id);
                    return audit;
                }
            }

            _logger.LogWarning("Failed to fetch audit with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching audit with ID {Id}", id);
            return null;
        }
    }

    public async Task<Audit> CreateAuditAsync(Audit audit)
    {
        try
        {
            _logger.LogInformation("Creating new audit for template: {TemplateId}", audit.AuditTemplateId);
            var response = await _httpClient.PostAsJsonAsync("api/v1/audits", audit);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Audit>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully created audit with ID {Id}", apiResponse.Data.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to create audit. Status: {StatusCode}", response.StatusCode);
            throw new InvalidOperationException("Failed to create audit");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit");
            throw;
        }
    }

    public async Task<string> CreateAuditAsync(CreateAuditRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new audit for template: {TemplateId}", request.AuditTemplateId);
            var response = await _httpClient.PostAsJsonAsync("api/v1/audits", request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<string>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && !string.IsNullOrEmpty(apiResponse.Data))
                {
                    _logger.LogInformation("Successfully created audit with ID {Id}", apiResponse.Data);
                    return apiResponse.Data;
                }
            }

            // Handle error response with detailed validation errors
            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to create audit. Status: {StatusCode}, Response: {ErrorContent}",
                response.StatusCode, errorContent);

            // Try to parse the error response to get detailed validation errors
            string detailedErrorMessage = "Failed to create audit";
            try
            {
                var errorResponse = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (errorResponse.TryGetProperty("errors", out var errorsElement))
                {
                    var errorMessages = new List<string>();

                    if (errorsElement.ValueKind == JsonValueKind.Object)
                    {
                        foreach (var error in errorsElement.EnumerateObject())
                        {
                            if (error.Value.ValueKind == JsonValueKind.Array)
                            {
                                foreach (var message in error.Value.EnumerateArray())
                                {
                                    errorMessages.Add($"{error.Name}: {message.GetString()}");
                                }
                            }
                        }
                    }

                    if (errorMessages.Any())
                    {
                        detailedErrorMessage = $"Validation failed: {string.Join("; ", errorMessages)}";
                    }
                }
                else if (errorResponse.TryGetProperty("detail", out var detailElement))
                {
                    detailedErrorMessage = detailElement.GetString() ?? detailedErrorMessage;
                }
            }
            catch (JsonException)
            {
                // If we can't parse the error response, use the original error content
                detailedErrorMessage = $"Failed to create audit. Server response: {errorContent}";
            }

            throw new InvalidOperationException(detailedErrorMessage);
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            _logger.LogError(ex, "Error creating audit");
            throw new InvalidOperationException("Failed to create audit", ex);
        }
    }

    public async Task<Audit> UpdateAuditAsync(Audit audit)
    {
        try
        {
            _logger.LogInformation("Updating audit with ID {Id}", audit.Id);
            var response = await _httpClient.PutAsJsonAsync($"api/v1/audits/{audit.Id}", audit);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Audit>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully updated audit with ID {Id}", audit.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to update audit with ID {Id}. Status: {StatusCode}", audit.Id, response.StatusCode);
            throw new InvalidOperationException("Failed to update audit");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating audit with ID {Id}", audit.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAuditAsync(string id)
    {
        try
        {
            _logger.LogInformation("Deleting audit with ID {Id}", id);
            var response = await _httpClient.DeleteAsync($"api/v1/audits/{id}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted audit with ID {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to delete audit with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting audit with ID {Id}", id);
            return false;
        }
    }

    public async Task<AuditReviewDto?> GetAuditForReviewAsync(string id)
    {
        try
        {
            _logger.LogInformation("Fetching audit {Id} for review from API", id);
            var response = await _httpClient.GetAsync($"api/v1/audits/{id}/review");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditReviewDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully fetched audit {Id} for review", id);
                    return apiResponse.Data;
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response for audit {Id} review: {Message}", id, apiResponse?.Message);
                    return null;
                }
            }
            else
            {
                _logger.LogWarning("Failed to fetch audit {Id} for review. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching audit {Id} for review", id);
            return null;
        }
    }

    public async Task<bool> ReviewAuditAsync(string id, ReviewAuditRequest request)
    {
        try
        {
            _logger.LogInformation("Reviewing audit {Id} with approval: {Approved}", id, request.Approved);

            var reviewCommand = new
            {
                Comments = request.ManagerComments,
                Approved = request.Approved
            };

            var response = await _httpClient.PostAsJsonAsync($"api/v1/audits/{id}/review", reviewCommand);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully reviewed audit {Id}", id);
                return true;
            }
            else
            {
                _logger.LogWarning("Failed to review audit {Id}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reviewing audit {Id}", id);
            return false;
        }
    }

    public async Task<IEnumerable<FindingDto>> GetAuditFindingsAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Fetching findings for audit {AuditId}", auditId);
            var response = await _httpClient.GetAsync($"api/v1/audits/{auditId}/findings");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<FindingDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to fetch findings for audit {AuditId}. Status: {StatusCode}", auditId, response.StatusCode);
            return Enumerable.Empty<FindingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching findings for audit {AuditId}", auditId);
            return Enumerable.Empty<FindingDto>();
        }
    }

    public async Task<PagedResult<FindingDto>> GetFindingsAsync(FindingFilterModel filter)
    {
        try
        {
            _logger.LogInformation("Fetching findings with filter");

            // Build query parameters
            var queryParams = new List<string>
            {
                $"pageNumber={filter.PageNumber}",
                $"pageSize={filter.PageSize}"
            };

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(filter.SearchTerm)}");

            if (filter.SeverityLevel.HasValue)
                queryParams.Add($"severityLevel={filter.SeverityLevel}");

            if (filter.CategoryId.HasValue)
                queryParams.Add($"categoryId={filter.CategoryId}");

            if (filter.IsOpen.HasValue)
                queryParams.Add($"isOpen={filter.IsOpen.Value.ToString().ToLower()}");

            if (filter.IsOverdue.HasValue)
                queryParams.Add($"isOverdue={filter.IsOverdue.Value.ToString().ToLower()}");

            if (!string.IsNullOrWhiteSpace(filter.ResponsibleUserId))
                queryParams.Add($"responsibleUserId={Uri.EscapeDataString(filter.ResponsibleUserId)}");

            if (filter.AreaId.HasValue)
                queryParams.Add($"areaId={filter.AreaId}");

            if (filter.CreatedAfter.HasValue)
                queryParams.Add($"createdAfter={filter.CreatedAfter.Value:yyyy-MM-dd}");

            if (filter.CreatedBefore.HasValue)
                queryParams.Add($"createdBefore={filter.CreatedBefore.Value:yyyy-MM-dd}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/v1/findings?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<FindingDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to fetch findings. Status: {StatusCode}", response.StatusCode);
            return new PagedResult<FindingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching findings");
            return new PagedResult<FindingDto>();
        }
    }

    public async Task<FindingStatisticsModel> GetFindingStatisticsAsync()
    {
        try
        {
            _logger.LogInformation("Fetching finding statistics");
            var response = await _httpClient.GetAsync("api/v1/findings/statistics");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingStatisticsModel>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to fetch finding statistics. Status: {StatusCode}", response.StatusCode);
            return new FindingStatisticsModel();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching finding statistics");
            return new FindingStatisticsModel();
        }
    }

    public async Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId)
    {
        try
        {
            _logger.LogInformation("Assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);

            var request = new AssignFindingCategoryRequest
            {
                FindingId = findingId,
                CategoryId = categoryId
            };

            var response = await _httpClient.PutAsJsonAsync($"api/v1/findings/{findingId}/category", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);
            return false;
        }
    }

    public async Task<bool> AssignFindingResponsibleUserAsync(string findingId, string? userId)
    {
        try
        {
            _logger.LogInformation("Assigning responsible user {UserId} to finding {FindingId}", userId, findingId);

            var request = new { ResponsibleUserId = userId };
            var response = await _httpClient.PutAsJsonAsync($"api/v1/findings/{findingId}/responsible", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning responsible user {UserId} to finding {FindingId}", userId, findingId);
            return false;
        }
    }

    public async Task<bool> BulkUpdateFindingsAsync(BulkFindingOperationModel operation)
    {
        try
        {
            _logger.LogInformation("Performing bulk operation {Operation} on {Count} findings", operation.Operation, operation.FindingIds.Count);

            var response = await _httpClient.PostAsJsonAsync("api/v1/findings/bulk", operation);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk operation {Operation}", operation.Operation);
            return false;
        }
    }
}
